package cn.pf.orch.meiye.service.system;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.assembler.ResourceAssembler;
import cn.pf.orch.meiye.assembler.RoleAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.system.command.MyChangeStatusCommand;
import cn.pf.orch.meiye.domain.system.command.MyRoleResourceCommand;
import cn.pf.orch.meiye.domain.system.command.MyRoleSaveCommand;
import cn.pf.orch.meiye.domain.system.command.MyRoleUpdateCommand;
import cn.pf.orch.meiye.domain.system.dto.MyResourceTreeDTO;
import cn.pf.orch.meiye.domain.system.dto.MyRoleDTO;
import cn.pf.orch.meiye.domain.system.po.*;
import cn.pf.orch.meiye.domain.system.query.MyRolePageQuery;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import cn.pf.orch.meiye.enums.StatusEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.*;
import cn.pf.orch.meiye.processor.RoleProcessor;
import cn.pf.orch.meiye.service.SsoService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RoleService {

    @Resource
    private MyRoleMapper myRoleMapper;
    @Resource
    private RoleAssembler assembler;
    @Resource
    private ResourceAssembler resourceAssembler;
    @Resource
    private RoleProcessor roleProcessor;
    @Resource
    private MyResourceMapper myResourceMapper;
    @Resource
    private MyRoleResourceRelMapper myRoleResourceRelMapper;
    @Resource
    private MyCompanyMapper myCompanyMapper;
    @Resource
    private MyUserRoleRelMapper myUserRoleRelMapper;
    @Resource
    private SsoService ssoService;

    public PageResultDTO<MyRoleDTO> page(MyRolePageQuery query) {
        boolean admin = CurrentUserHolder.isAdmin();//用于限制角色类型
        List<Long> companyIds = CurrentUserHolder.getRangeCompanyIds();
        if(admin){
            companyIds.add(0L);
        }
        if(StrUtil.isNotBlank(query.getCompanyName())){
            List<MyCompanyPO> myCompanyPOS = myCompanyMapper.selectByLikeName(query.getCompanyName());
            if(CollUtil.isEmpty(myCompanyPOS)){
                return PageResultDTO.empty(query.getPageNo(), query.getPageSize());
            }
            companyIds = myCompanyPOS.stream().map(MyCompanyPO::getId).filter(companyIds::contains).collect(Collectors.toList());
        }
        PageResultDTO<MyRoleDTO> pageResult = assembler.toPage(myRoleMapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query, companyIds,admin));
        return pageResult;
    }

    public MyRoleDTO get(Long id) {
        return myRoleMapper.selectDTOById(id);
    }

    public Boolean save(MyRoleSaveCommand command) {
        roleProcessor.checkSave(command);
        MyRolePO po = new MyRolePO();
        po.setName(command.getName());
        po.setAuthType(command.getAuthType());
        po.setCompanyId(command.getCompanyId());
        po.setType(RoleTypeEnum.CUSTOMIZE);
        po.setStatus(StatusEnum.ENABLE);
        po.setRemark("");
        myRoleMapper.insert(po);
        return true;
    }

    public Boolean change(MyRoleUpdateCommand command) {
        roleProcessor.checkChange(command);
        MyRolePO po = new MyRolePO();
        po.setName(command.getName());
        po.setId(command.getId());
        myRoleMapper.updateById(po);
        return true;
    }

    public Boolean changeStatus(MyChangeStatusCommand command) {
        LambdaUpdateWrapper<MyRolePO> wrapper = Wrappers.lambdaUpdate(MyRolePO.class)
                .in(MyRolePO::getId, command.getIdList())
                .set(MyRolePO::getStatus, command.getStatus());
        return myRoleMapper.update(wrapper) != 0;
    }

    public Boolean batchRemove(List<Long> idList) {
        // 系统角色禁止删除
        List<MyRolePO> upmRolePOS = myRoleMapper.selectBatchIds(idList);
        if (upmRolePOS.stream().anyMatch(role -> !RoleTypeEnum.CUSTOMIZE.equals(role.getType()))) {
            throw new BusinessException(MessageCode.ROLE_SYSTEM_NOT_DELETE_ERROR);
        }
        //用户使用角色禁止删除
        List<MyUserRoleRelPO> myUserRoleRelPOS = myUserRoleRelMapper.selectByRoleIds(idList);
        if(CollUtil.isNotEmpty(myUserRoleRelPOS)){
            throw new BusinessException(MessageCode.ROLE_USER_ERROR);
        }
        myRoleMapper.deleteBatchIds(idList);
        return true;
    }

    public List<MyRoleDTO> selectBatchIds(List<Long> idList) {
        return assembler.PO2DTO(myRoleMapper.selectBatchIds(idList));
    }


    public List<MyResourceTreeDTO> queryResourceTreeById(Long id) {
        List<MyResourcePO> upmResourcePOS = myResourceMapper.selectByRoleId(id);
        return resourceAssembler.buildTree(upmResourcePOS);
    }

    public Boolean changeResource(MyRoleResourceCommand command) {
        myRoleResourceRelMapper.deleteByRoleId(command.getRoleId());
        if (ObjUtil.isNotNull(command.getResourceIdList())){
            List<MyRoleResourceRelPO> roleResourceList = command.getResourceIdList().stream().map(resourceId -> {
                        return new MyRoleResourceRelPO().setRoleId(command.getRoleId()).setResourceId(resourceId);
                    }
            ).collect(Collectors.toList());
            myRoleResourceRelMapper.saveBatch(roleResourceList);
        }
        //刷新用户角色缓存
        List<MyUserRoleRelPO> myUserRoleRelPOS = myUserRoleRelMapper.selectByRoleIds(Collections.singletonList(command.getRoleId()));
        if(CollUtil.isNotEmpty(myUserRoleRelPOS)){
            myUserRoleRelPOS.forEach(myUserRoleRelPO -> {
                ssoService.logoutByOpenId(Collections.singletonList(myUserRoleRelPO.getUserId()));
            });
        }
        return true;
    }

    public List<MyRoleDTO> queryByCompanyId(Long companyId){
        return assembler.PO2DTO(myRoleMapper.selectByCompanyId(companyId));
    }


}
