package cn.july.orch.meeting.controller.system;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "系统管理-角色管理")
@RestController
@RequestMapping("/role")
public class RoleController {

    @Resource
    private RoleService roleService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询角色")
    public PageResultDTO<MyRoleDTO> page(@ApiParam(value = "查询类") @RequestBody @Validated MyRolePageQuery query) {
        return roleService.page(query);
    }

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public MyRoleDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id) {
        return roleService.get(id);
    }

    @PostMapping("/save")
    @ApiOperation(value = "添加角色")
    public Boolean save(@ApiParam(value = "角色") @RequestBody @Validated MyRoleSaveCommand command) {
        return roleService.save(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改角色")
    public Boolean change(@ApiParam(value = "角色") @RequestBody @Validated MyRoleUpdateCommand command) {
        return roleService.change(command);
    }

    @PostMapping("/change/status")
    @ApiOperation(value = "批量启用停用角色")
    public Boolean changeStatus(@ApiParam(value = "批量启用禁用角色") @RequestBody @Validated MyChangeStatusCommand command) {
        return roleService.changeStatus(command);
    }

    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除角色")
    public Boolean batchRemove(@ApiParam(value = "批量删除角色") @RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return roleService.batchRemove(idList);
    }

    @PostMapping("/batch/search")
    @ApiOperation(value = "依照角色id列表批量获取角色信息")
    public List<MyRoleDTO> bachSearch(@ApiParam(value = "依照id列表批量获取角色信息") @RequestBody List<Long> idList) {
        return roleService.selectBatchIds(idList);
    }

//    @PostMapping("/query/resource")
//    @ApiOperation(value = "查询角色id资源")
//    public List<MyResourceTreeDTO> queryResourceTreeById(@ApiParam(value = "角色id", name = "id", required = true) @RequestParam Long id) {
//        return roleService.queryResourceTreeById(id);
//    }

//    @PostMapping("/update/resource")
//    @ApiOperation(value = "修改角色下菜单权限")
//    public Boolean changeResource(@ApiParam(value = "角色id") @RequestBody @Validated MyRoleResourceCommand command) {
//        return roleService.changeResource(command);
//    }

}

