package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 系统资源PO
 * @date 2025-01-24
 */
@Data
@Accessors(chain = true)
@TableName(value = "sys_resource", autoResultMap = true)
public class SysResourcePO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 资源类型（1: 菜单，2：按钮）
     */
    @TableField("type")
    private Integer type;

    /**
     * 菜单、面包屑、多标签页显示的名称
     */
    @TableField("title")
    private String title;

    /**
     * 排序
     */
    @TableField("resource_sort")
    private Integer resourceSort;

    /**
     * 上级菜单
     */
    @TableField("pid")
    private Long pid;

    /**
     * 前端path
     */
    @TableField("path")
    private String path;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 按钮权限标识
     */
    @TableField("auths")
    private String auths;

    /**
     * 是否在菜单中显示（默认值：true）
     */
    @TableField("show_link")
    private Integer showLink;

    /**
     * 是否显示父级菜单（默认值：true）
     */
    @TableField("show_parent")
    private Integer showParent;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
}